#ifndef CIRCUIT_CONFIG_H
#define CIRCUIT_CONFIG_H

/*
 * 电路网络查询系统配置文件
 * 统一管理系统中所有固定常量和配置参数
 * 作者: 电路网络查询系统
 * 版本: 1.0
 */

// ==================== 电路基本参数 ====================

// 输入电压 (单位: V) - 系统固定输入电压值
#define INPUT_VOLTAGE_VALUE 24.0

// 负载电阻 (单位: Ω) - 系统固定负载电阻值  
#define LOAD_RESISTANCE_VALUE 200.0

// ==================== 显示格式配置 ====================

// 数值显示精度 - 计算结果保留的小数位数
#define DISPLAY_DECIMAL_PRECISION 6

// 格式化字符串 - 用于printf显示双精度浮点数
#define DOUBLE_FORMAT_STRING "%.6lf"

// ==================== 学号参数计算配置 ====================

// 网络序号基数 - 学号后两位需要加上的固定值
#define NETWORK_NUMBER_BASE 10

// 支路电阻RB基数 - 学号最后一位需要加上的固定值
#define BRANCH_RESISTANCE_B_BASE 1

// 支路电阻RT基数 - 学号最后一位需要加上的固定值  
#define BRANCH_RESISTANCE_T_BASE 199

// ==================== 系统限制参数 ====================

// 学号最大长度 - 用于输入验证
#define MAX_STUDENT_ID_LENGTH 20

// 学号最小长度 - 确保至少有两位数字
#define MIN_STUDENT_ID_LENGTH 2

// ==================== 错误代码定义 ====================

// 查询失败返回值 - 当查询索引超出范围时返回
#define QUERY_ERROR_CODE -1.0

// 参数计算成功返回值
#define CALCULATION_SUCCESS 0

// 参数计算失败返回值
#define CALCULATION_FAILURE -1

// ==================== 菜单选项定义 ====================

// 主菜单选项数量
#define MENU_OPTION_COUNT 5

// 菜单选项: 计算电路参数
#define MENU_CALCULATE_CIRCUIT 1

// 菜单选项: 查询节点电压
#define MENU_QUERY_NODE_VOLTAGE 2

// 菜单选项: 查询支路RB电流
#define MENU_QUERY_BRANCH_CURRENT_B 3

// 菜单选项: 查询支路RT电流  
#define MENU_QUERY_BRANCH_CURRENT_T 4

// 菜单选项: 退出程序
#define MENU_EXIT_PROGRAM 5

#endif // CIRCUIT_CONFIG_H
