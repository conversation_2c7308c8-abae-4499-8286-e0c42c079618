#ifndef Circuit_Query_H
#define Circuit_Query_H

#include<stdio.h>
#include<stdlib.h>

//定义节点结构体
typedef struct node
{
	double node_voltage; //存储一个节点电位
}node_t;
//定义支路结构体
typedef struct branch
{
	double current_t;
	double current_b; //存储一个支路电流
}branch_t;

//函数声明
void calculate_req(int network_num, double resistance_b, double resistance_t, double resistance_l, double req[]);
void calculate_node_voltage(node_t* nodes, int network_num, double input_voltage, double req[], double resistance_b, double resistance_t, double resistance_l);
void calculate_branch_current(branch_t* branches, node_t* nodes, int network_num, double input_voltage, double resistance_b, double resistance_t);
double query_node_voltage(node_t* nodes, int network_num, int node_index);
double query_branch_current_b(branch_t* branches, int network_num, int branch_index);
double query_branch_current_t(branch_t* branches, int network_num, int branch_index);
#endif
