#ifndef CIRCUIT_QUERY_H
#define CIRCUIT_QUERY_H

#include <stdio.h>
#include <stdlib.h>
#include <math.h>  // 添加math.h用于fabs函数

// 节点结构体（蛇形命名法）
typedef struct node {
    double node_voltage;  // 节点电位
} node_t;

// 支路结构体（蛇形命名法）
typedef struct branch {
    double current_b;     // RB支路电流
    double current_t;     // RT支路电流
} branch_t;

// ================== 核心计算函数 ==================
// 计算等效电阻数组
void calculate_req(int network_num, double rb, double rt, double rl, double* req_array);

// 计算节点电压
void calculate_node_voltage(node_t* node_array, int network_num, 
                           double input_voltage, double* req_array, 
                           double rb, double rt, double rl);

// 计算支路电流
void calculate_branch_current(branch_t* branch_array, node_t* node_array, 
                            int network_num, double input_voltage, 
                            double rb, double rt);

// ================== 查询功能函数 ==================
// 查询节点电压
double query_node_voltage(node_t* node_array, int network_num, int node_index);

// 查询RB支路电流
double query_branch_current_b(branch_t* branch_array, int network_num, int branch_index);

// 查询RT支路电流
double query_branch_current_t(branch_t* branch_array, int network_num, int branch_index);

// ================== 内存管理函数 ==================
// 安全分配电路内存
int allocate_circuit_memory(int network_num, double** req_array, 
                          node_t** node_array, branch_t** branch_array);

// 释放电路内存
void free_circuit_memory(double** req_array, node_t** node_array, branch_t** branch_array);

// ================== 输入验证函数 ==================
// 验证网络序号
int validate_network_number(int network_num);

// 验证正值
int validate_positive_value(double value, const char* value_name);

// 验证所有输入参数
int validate_input_parameters(int network_num, double input_voltage, 
                            double rb, double rt, double rl);

// ================== 数值安全函数 ==================
// 安全除法（防止除零错误）
static inline double safe_division(double numerator, double denominator) {
    return (fabs(denominator) < 1e-10) ? 0.0 : numerator / denominator;
}

// 检查是否为零值
static inline int is_zero_value(double value) {
    return fabs(value) < 1e-10;
}

#endif // CIRCUIT_QUERY_H