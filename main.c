#include "main.h"
#include <windows.h>

// 等待按键的函数
static void wait_for_any_key(void) {
    printf("请输入任意键返回...");
    getchar(); // 清除输入缓冲区中的换行符
    getchar(); // 等待用户按键
}

// 主菜单函数
static void display_menu(void) {
    printf("\n电路网络查询系统\n");
    printf("请选择操作：\n");
    printf("1. 输入电路参数并计算\n");
    printf("2. 查询节点电压\n");
    printf("3. 查询RB支路电流\n");
    printf("4. 查询RT支路电流\n");
    printf("5. 退出程序\n");
    printf("请输入选项(1-5):");
}

// 输入电路参数的函数
static void input_circuit_parameters(int* network_num, double* input_voltage, 
                                    double* rb, double* rt, double* rl) {
    printf("\n请输入网络序号N：");
    scanf_s("%d", network_num);
    printf("\n请输入电压Un：");
    scanf_s("%lf", input_voltage);
    printf("\n请输入支路电阻RB：");
    scanf_s("%lf", rb);
    printf("\n请输入支路电阻RT：");
    scanf_s("%lf", rt);
    printf("\n请输入负载电阻RL：");
    scanf_s("%lf", rl);
}

// 分配内存的函数
static int allocate_memory(int network_num, double** req_array, 
                          Node** node_array, Branch** branch_array) {
    *req_array = (double*)malloc(network_num * sizeof(double));
    if (*req_array == NULL) return 0;
    
    *node_array = (Node*)malloc(network_num * sizeof(Node));
    if (*node_array == NULL) {
        free(*req_array);
        return 0;
    }
    
    *branch_array = (Branch*)malloc(network_num * sizeof(Branch));
    if (*branch_array == NULL) {
        free(*req_array);
        free(*node_array);
        return 0;
    }
    return 1;
}

int main() {
    int choice;
    int network_num = 0;
    double input_voltage = 0.0;
    double rb = 0.0, rt = 0.0, rl = 0.0;
    double* req_array = NULL;
    Node* node_array = NULL;
    Branch* branch_array = NULL;

    while (1) {
        system("cls");
        display_menu();
        scanf_s("%d", &choice);
        
        switch (choice) {
        case 1:  // 输入参数并计算
            input_circuit_parameters(&network_num, &input_voltage, &rb, &rt, &rl);
            
            if (!allocate_memory(network_num, &req_array, &node_array, &branch_array)) {
                printf("内存分配失败！！！\n");
                exit(1);
            }
            
            Calculate_Req(network_num, rb, rt, rl, req_array);
            Calculate_Node_Voltage(node_array, network_num, input_voltage, req_array, rb, rt, rl);
            Calculate_Branch_Current(branch_array, node_array, network_num, input_voltage, rb, rt);
            
            printf("等效电阻 = %.6lfΩ\n", req_array[0]);
            printf("负载电流 = %.6lfA\n", input_voltage / req_array[0]);
            wait_for_any_key();
            break;
            
        case 2:  // 查询节点电压
            if (node_array == NULL) {
                printf("请先输入电路参数并计算（在主菜单选择操作1）.\n");
                wait_for_any_key();
                break;
            }
            
            printf("请输入要查询的节点序号（0-%d）:", network_num - 1);
            int node_index;
            scanf_s("%d", &node_index);
            
            double node_voltage = Query_Node_Voltage(node_array, network_num, node_index);
            if (node_voltage != -1) {
                printf("节点%d的电压为：%.6lfV\n", node_index, node_voltage);
            } else {
                printf("输入了错误的节点序号，查询无效！！！");
            }
            wait_for_any_key();
            break;
            
        case 3:  // 查询RB支路电流
            if (branch_array == NULL) {
                printf("请先输入电路参数并计算（在主菜单选择操作1）.\n");
                wait_for_any_key();
                break;
            }
            
            printf("请输入要查询的水平支路序号（0-%d）:", network_num - 1);
            int branch_index_b;
            scanf_s("%d", &branch_index_b);
            
            double current_b = Query_Branch_Current_B(branch_array, network_num, branch_index_b);
            if (current_b != -1) {
                printf("支路RB%d的电流为：%.6lfA\n", branch_index_b, current_b);
            } else {
                printf("输入了错误的支路序号，查询无效！！！");
            }
            wait_for_any_key();
            break;
            
        case 4:  // 查询RT支路电流
            if (branch_array == NULL) {
                printf("请先输入电路参数并计算（在主菜单选择操作1）.\n");
                wait_for_any_key();
                break;
            }
            
            printf("请输入要查询的垂直支路序号（0-%d）:", network_num - 1);
            int branch_index_t;
            scanf_s("%d", &branch_index_t);
            
            double current_t = Query_Branch_Current_T(branch_array, network_num, branch_index_t);
            if (current_t != -1) {
                printf("支路RT%d的电流为：%.6lfA\n", branch_index_t, current_t);
            } else {
                printf("输入了错误的支路序号，查询无效！！！");
            }
            wait_for_any_key();
            break;
            
        case 5:  // 退出程序
            free(node_array);
            free(branch_array);
            free(req_array);
            req_array = NULL;
            exit(0);
            
        default:
            printf("无效的操作序号，请重新输入！！！");
            wait_for_any_key();
            break;
        }
    }
    return 0;
}