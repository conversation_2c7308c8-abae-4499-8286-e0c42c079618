
#include"main.h"
#include"config.h"
#include<windows.h>
void Menu()
{
	printf("\n===========================================\n");
	printf("        电路网络查询系统 v1.0\n");
	printf("===========================================\n");
	printf("请选择操作：\n");
	printf("1. 输入电路参数并计算结果\n");
	printf("2. 查询节点电压\n");
	printf("3. 查询支路RB电流\n");
	printf("4. 查询支路RT电流\n");
	printf("5. 退出程序\n");
	printf("-------------------------------------------\n");
	printf("请输入选项(1-5): ");
}
int main()
{
	int choice;
	int N;
	double Un;
	double RB, RT, RL;
	double* Req = NULL;
	Node* nodes = NULL;
	Branch* branches = NULL;
	while (1)
	{
		system("cls");
		Menu();
		scanf_s("%d", &choice);
		switch (choice)
		{
		case 1:
			printf("\n=== 电路参数输入 ===\n");
			printf("请输入网络序号N (建议范围: 10-99): ");
			scanf_s("%d", &N);
			printf("请输入输入电压Un (单位: V): ");
			scanf_s("%lf", &Un);
			printf("请输入支路电阻RB (单位: Ω): ");
			scanf_s("%lf", &RB);
			printf("请输入支路电阻RT (单位: Ω): ");
			scanf_s("%lf", &RT);
			printf("请输入负载电阻RL (单位: Ω): ");
			scanf_s("%lf", &RL);
			Req = (double*)malloc(N * sizeof(double));
			if (Req == NULL)
			{
				printf("错误：内存分配失败，程序将退出\n");
				exit(1);
			}
			nodes = (Node*)malloc(N * sizeof(Node));
			if (nodes == NULL)
			{
				printf("错误：内存分配失败，程序将退出\n");
				free(Req);
				exit(1);
			}
			branches = (Branch*)malloc(N * sizeof(Branch));
			if (branches == NULL)
			{
				printf("错误：内存分配失败，程序将退出\n");
				free(nodes);
				free(Req);
				exit(1);
			}
			Calculate_Req(N, RB, RT, RL, Req);
			Calculate_Node_Voltage(nodes, N, Un, Req, RB, RT, RL);
			Calculate_Branch_Current(branches,nodes, N, Un, RB, RT);
			printf("\n=== 计算结果 ===\n");
			printf("  等效电阻 Req = " DOUBLE_FORMAT_STRING " Ω\n", Req[0]);
			printf("  负载电流 IL  = " DOUBLE_FORMAT_STRING " A\n", Un / Req[0]);
			printf("==================\n");
			printf("计算完成！按任意键继续...");
			getchar();
			getchar();
			break;
		case 2:
			if (nodes == NULL)
			{
				printf("请先执行选项1进行电路参数计算。\n");
				printf("按任意键继续...");
				getchar();
				getchar();
				break;
			}
			printf("\n=== 节点电压查询 ===\n");
			printf("请输入要查询的节点编号 (范围: 0-%d): ", N - 1);
			int nodeIndex;
			scanf_s("%d", &nodeIndex);
			double Node_Voltage = Query_Node_Voltage(nodes, N, nodeIndex);
			if (Node_Voltage != -1)
			{
				printf("节点%d的电压为: " DOUBLE_FORMAT_STRING " V\n", nodeIndex, Node_Voltage);
			}
			else
			{
				printf("输入的节点编号超出范围，查询无效！\n");
			}
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		case 3:
			if (branches == NULL)
			{
				printf("请先执行选项1进行电路参数计算。\n");
				printf("按任意键继续...");
				getchar();
				getchar();
				break;
			}
			printf("\n=== 支路RB电流查询 ===\n");
			printf("请输入要查询的支路编号 (范围: 0-%d): ", N - 1);
			int BranchIndex;
			scanf_s("%d", &BranchIndex);
			double current = Query_Branch_Current_B(branches, N, BranchIndex);
			if (current != -1)
			{
				printf("支路RB%d的电流为: " DOUBLE_FORMAT_STRING " A\n", BranchIndex, current);
			}
			else
			{
				printf("输入的支路编号超出范围，查询无效！\n");
			}
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		case 4:
			if (branches == NULL)
			{
				printf("请先执行选项1进行电路参数计算。\n");
				printf("按任意键继续...");
				getchar();
				getchar();
				break;
			}
			printf("\n=== 支路RT电流查询 ===\n");
			printf("请输入要查询的支路编号 (范围: 0-%d): ", N - 1);
			int branchIndex;
			scanf_s("%d", &branchIndex);
			double Current = Query_Branch_Current_T(branches, N, branchIndex);
			if (Current != -1)
			{
				printf("支路RT%d的电流为: " DOUBLE_FORMAT_STRING " A\n", branchIndex, Current);
			}
			else
			{
				printf("输入的支路编号超出范围，查询无效！\n");
			}
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		case 5:
			printf("\n感谢使用电路网络查询系统，程序即将退出...\n");
			free(nodes);
			free(branches);
			free(Req);
			Req = NULL;
			getchar();
			getchar();
			exit(0);
		default:
			printf("\n无效的选项编号，请输入1-5之间的数字！\n");
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		}
	}
}