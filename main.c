
#include"main.h"
#include"config.h"
#include<windows.h>
void Menu()
{
	printf("\n===========================================\n");
	printf("        电路网络查询系统 v1.0\n");
	printf("===========================================\n");
	printf("请选择操作：\n");
	printf("1. 输入电路参数并计算结果\n");
	printf("2. 查询节点电压\n");
	printf("3. 查询支路RB电流\n");
	printf("4. 查询支路RT电流\n");
	printf("5. 退出程序\n");
	printf("-------------------------------------------\n");
	printf("请输入选项(1-5): ");
}
int main()
{
	int choice;
	int network_num;
	double input_voltage;
	double resistance_b, resistance_t, resistance_l;
	double* req = NULL;
	node_t* nodes = NULL;
	branch_t* branches = NULL;
	while (1)
	{
		system("cls");
		Menu();
		scanf_s("%d", &choice);
		switch (choice)
		{
		case 1:
			printf("\n=== 电路参数输入 ===\n");
			printf("请输入网络序号N (建议范围: 10-99): ");
			scanf_s("%d", &network_num);
			printf("请输入输入电压Un (单位: V): ");
			scanf_s("%lf", &input_voltage);
			printf("请输入支路电阻RB (单位: Ω): ");
			scanf_s("%lf", &resistance_b);
			printf("请输入支路电阻RT (单位: Ω): ");
			scanf_s("%lf", &resistance_t);
			printf("请输入负载电阻RL (单位: Ω): ");
			scanf_s("%lf", &resistance_l);
			req = (double*)malloc(network_num * sizeof(double));
			if (req == NULL)
			{
				printf("错误：内存分配失败，程序将退出\n");
				exit(1);
			}
			nodes = (node_t*)malloc(network_num * sizeof(node_t));
			if (nodes == NULL)
			{
				printf("错误：内存分配失败，程序将退出\n");
				free(req);
				exit(1);
			}
			branches = (branch_t*)malloc(network_num * sizeof(branch_t));
			if (branches == NULL)
			{
				printf("错误：内存分配失败，程序将退出\n");
				free(nodes);
				free(req);
				exit(1);
			}
			calculate_req(network_num, resistance_b, resistance_t, resistance_l, req);
			calculate_node_voltage(nodes, network_num, input_voltage, req, resistance_b, resistance_t, resistance_l);
			calculate_branch_current(branches, nodes, network_num, input_voltage, resistance_b, resistance_t);
			printf("\n=== 计算结果 ===\n");
			printf("  等效电阻 Req = " DOUBLE_FORMAT_STRING " Ω\n", req[0]);
			printf("  负载电流 IL  = " DOUBLE_FORMAT_STRING " A\n", input_voltage / req[0]);
			printf("==================\n");
			printf("计算完成！按任意键继续...");
			getchar();
			getchar();
			break;
		case 2:
			if (nodes == NULL)
			{
				printf("请先执行选项1进行电路参数计算。\n");
				printf("按任意键继续...");
				getchar();
				getchar();
				break;
			}
			printf("\n=== 节点电压查询 ===\n");
			printf("请输入要查询的节点编号 (范围: 0-%d): ", network_num - 1);
			int node_index;
			scanf_s("%d", &node_index);
			double node_voltage = query_node_voltage(nodes, network_num, node_index);
			if (node_voltage != -1)
			{
				printf("节点%d的电压为: " DOUBLE_FORMAT_STRING " V\n", node_index, node_voltage);
			}
			else
			{
				printf("输入的节点编号超出范围，查询无效！\n");
			}
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		case 3:
			if (branches == NULL)
			{
				printf("请先执行选项1进行电路参数计算。\n");
				printf("按任意键继续...");
				getchar();
				getchar();
				break;
			}
			printf("\n=== 支路RB电流查询 ===\n");
			printf("请输入要查询的支路编号 (范围: 0-%d): ", network_num - 1);
			int branch_index_b;
			scanf_s("%d", &branch_index_b);
			double current_b = query_branch_current_b(branches, network_num, branch_index_b);
			if (current_b != -1)
			{
				printf("支路RB%d的电流为: " DOUBLE_FORMAT_STRING " A\n", branch_index_b, current_b);
			}
			else
			{
				printf("输入的支路编号超出范围，查询无效！\n");
			}
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		case 4:
			if (branches == NULL)
			{
				printf("请先执行选项1进行电路参数计算。\n");
				printf("按任意键继续...");
				getchar();
				getchar();
				break;
			}
			printf("\n=== 支路RT电流查询 ===\n");
			printf("请输入要查询的支路编号 (范围: 0-%d): ", network_num - 1);
			int branch_index_t;
			scanf_s("%d", &branch_index_t);
			double current_t = query_branch_current_t(branches, network_num, branch_index_t);
			if (current_t != -1)
			{
				printf("支路RT%d的电流为: " DOUBLE_FORMAT_STRING " A\n", branch_index_t, current_t);
			}
			else
			{
				printf("输入的支路编号超出范围，查询无效！\n");
			}
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		case 5:
			printf("\n感谢使用电路网络查询系统，程序即将退出...\n");
			free(nodes);
			free(branches);
			free(req);
			req = NULL;
			getchar();
			getchar();
			exit(0);
		default:
			printf("\n无效的选项编号，请输入1-5之间的数字！\n");
			printf("按任意键继续...");
			getchar();
			getchar();
			break;
		}
	}
}