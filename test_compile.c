#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// 测试输入输出功能
static void test_input_output(void) {
    int input_value;
    printf("请输入一个整数: ");
    if (scanf("%d", &input_value) == 1) {
        printf("您输入的整数是: %d\n", input_value);
    } else {
        printf("输入无效!\n");
        // 清除输入缓冲区
        while (getchar() != '\n');
    }
}

// 测试数学函数功能
static void test_math_functions(void) {
    double test_value = -3.14;
    double absolute_value = fabs(test_value);
    printf("fabs(%.2f) = %.2f\n", test_value, absolute_value);
    
    // 测试平方根函数
    double square_root = sqrt(16.0);
    printf("sqrt(16.0) = %.2f\n", square_root);
    
    // 测试幂函数
    double power_result = pow(2.0, 3.0);
    printf("pow(2.0, 3.0) = %.2f\n", power_result);
}

// 主测试函数
int main() {
    printf("=== 基本功能测试程序 ===\n");
    
    // 测试数学函数
    printf("\n[数学函数测试]\n");
    test_math_functions();
    
    // 测试输入输出
    printf("\n[输入输出测试]\n");
    test_input_output();
    
    // 内存分配测试
    printf("\n[内存分配测试]\n");
    int* number_array = malloc(5 * sizeof(int));
    if (number_array) {
        for (int i = 0; i < 5; i++) {
            number_array[i] = i * 10;
        }
        printf("分配的内存内容: ");
        for (int i = 0; i < 5; i++) {
            printf("%d ", number_array[i]);
        }
        printf("\n");
        free(number_array);
    } else {
        printf("内存分配失败!\n");
    }
    
    printf("\n=== 测试完成 ===\n");
    return 0;
}