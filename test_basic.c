#include "main.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// 电路网络查询系统基本功能测试
int main() {
    printf("=== 电路网络查询系统基本功能测试 ===\n");
    
    // 测试参数（使用蛇形命名法）
    int network_num = 3;
    double rb = 2.0;         // 支路电阻RB
    double rt = 200.0;       // 支路电阻RT
    double rl = 200.0;       // 负载电阻RL
    double input_voltage = 24.0;  // 输入电压
    
    printf("测试参数:\n");
    printf("网络序号: %d\n", network_num);
    printf("支路电阻RB: %.2f Ω\n", rb);
    printf("支路电阻RT: %.2f Ω\n", rt);
    printf("负载电阻RL: %.2f Ω\n", rl);
    printf("输入电压: %.2f V\n", input_voltage);
    
    // 分配内存（使用蛇形命名变量）
    double* req_array = NULL;
    node_t* node_array = NULL;
    branch_t* branch_array = NULL;
    
    // 使用内存分配函数
    if (!allocate_circuit_memory(network_num, &req_array, &node_array, &branch_array)) {
        printf("内存分配失败!\n");
        return -1;
    }
    
    // 测试计算函数
    printf("\n=== 开始计算 ===\n");
    
    // 计算等效电阻
    calculate_req(network_num, rb, rt, rl, req_array);
    printf("等效电阻计算完成\n");
    
    // 计算节点电压
    calculate_node_voltage(node_array, network_num, input_voltage, 
                          req_array, rb, rt, rl);
    printf("节点电压计算完成\n");
    
    // 计算支路电流
    calculate_branch_current(branch_array, node_array, network_num, 
                           input_voltage, rb, rt);
    printf("支路电流计算完成\n");
    
    // 显示结果
    printf("\n=== 计算结果 ===\n");
    printf("等效电阻: %.6f Ω\n", req_array[0]);
    printf("负载电流: %.6f A\n", safe_division(input_voltage, req_array[0]));
    
    for (int i = 0; i < network_num; i++) {
        printf("\n节点 %d:\n", i);
        printf("  电压: %.6f V\n", node_array[i].node_voltage);
        printf("  RB支路电流: %.6f A\n", branch_array[i].current_b);
        printf("  RT支路电流: %.6f A\n", branch_array[i].current_t);
    }
    
    // 测试查询函数
    printf("\n=== 测试查询功能 ===\n");
    int test_index = 1;
    
    double voltage = query_node_voltage(node_array, network_num, test_index);
    printf("查询节点 %d 电压: %.6f V\n", test_index, voltage);
    
    double current_b = query_branch_current_b(branch_array, network_num, test_index);
    printf("查询支路 %d RB电流: %.6f A\n", test_index, current_b);
    
    double current_t = query_branch_current_t(branch_array, network_num, test_index);
    printf("查询支路 %d RT电流: %.6f A\n", test_index, current_t);
    
    // 边界测试
    printf("\n=== 边界测试 ===\n");
    int invalid_index = network_num + 1;
    double invalid_voltage = query_node_voltage(node_array, network_num, invalid_index);
    printf("无效节点查询 (%d): %s\n", invalid_index, 
          (invalid_voltage == -1) ? "失败 (符合预期)" : "成功 (不符合预期)");
    
    // 释放内存
    free_circuit_memory(&req_array, &node_array, &branch_array);
    
    printf("\n=== 测试完成 ===\n");
    return 0;
}