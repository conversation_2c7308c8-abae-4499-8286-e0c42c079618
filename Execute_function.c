#include"main.h"
//����Req
void Calculate_Req(int N, double RB, double RT, double RL,double Req[])
{
	Req[N] = RL;
	for (int i = N; i >=1; i--)
	{
		Req[i-1] = (Req[i] * RT) / (RT + Req[i]);
		Req[i-1] += RB;
	}
}
//����ڵ��λ
void Calculate_Node_Voltage(Node* nodes, int N, double Un, double Req[], double RB, double RT, double RL) 
{
	double Total_Current = Un / Req[0];//�ܵ���
	int i;
	for (i = 0; i < N; i++)
	{
		if (i == 0)
		{
			nodes[i].Node_Voltage = Un - Total_Current * RB;
		}
		else
		{
			double temp = nodes[i - 1].Node_Voltage / (RT + Req[i + 1]);
				nodes[i].Node_Voltage = nodes[i - 1].Node_Voltage - temp * RB;
		}
	}
}
//����֧·����
void Calculate_Branch_Current(Branch* branches, Node* nodes, int N, double Un, double RB, double RT)
{
	int i;
	branches[0].Current_B = (Un - nodes[0].Node_Voltage) / RB;
	for (i = 1; i < N; i++)
	{
		branches[i].Current_B = (nodes[i - 1].Node_Voltage - nodes[i].Node_Voltage) / RB;
	}
	for (i = 0; i < N; i++)
	{
		branches[i].Current_T =  nodes[i].Node_Voltage / RT;
	}
}
//��ѯ�ڵ��ѹ
double Query_Node_Voltage(Node* nodes, int N, int nodeIndex)
{
	if (nodeIndex >= 0 && nodeIndex < N)
	{
		return nodes[nodeIndex].Node_Voltage;
	}
	else
	{
		return -1;
	}
}
//��ѯ֧·����RB
double Query_Branch_Current_B(Branch* branches, int N, int branchIndex)
{
	if (branchIndex >= 0 && branchIndex < N)
	{
		return branches[branchIndex].Current_B;
	}
	else
	{
		return -1;
	}
}
//��ѯ֧·����RT
double Query_Branch_Current_T(Branch* branches, int N, int branchIndex)
{
	if (branchIndex >= 0 && branchIndex < N)
	{
		return branches[branchIndex].Current_T;
	}
	else
	{
		return -1;
	}
}