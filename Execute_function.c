#include"main.h"
//计算等效电阻
void calculate_req(int network_num, double resistance_b, double resistance_t, double resistance_l, double req[])
{
	req[network_num] = resistance_l;
	for (int i = network_num; i >= 1; i--)
	{
		req[i-1] = (req[i] * resistance_t) / (resistance_t + req[i]);
		req[i-1] += resistance_b;
	}
}
//计算节点电位
void calculate_node_voltage(node_t* nodes, int network_num, double input_voltage, double req[], double resistance_b, double resistance_t, double resistance_l)
{
	double total_current = input_voltage / req[0]; //总电流
	int i;
	for (i = 0; i < network_num; i++)
	{
		if (i == 0)
		{
			nodes[i].node_voltage = input_voltage - total_current * resistance_b;
		}
		else
		{
			double temp = nodes[i - 1].node_voltage / (resistance_t + req[i + 1]);
			nodes[i].node_voltage = nodes[i - 1].node_voltage - temp * resistance_b;
		}
	}
}
//计算支路电流
void calculate_branch_current(branch_t* branches, node_t* nodes, int network_num, double input_voltage, double resistance_b, double resistance_t)
{
	int i;
	branches[0].current_b = (input_voltage - nodes[0].node_voltage) / resistance_b;
	for (i = 1; i < network_num; i++)
	{
		branches[i].current_b = (nodes[i - 1].node_voltage - nodes[i].node_voltage) / resistance_b;
	}
	for (i = 0; i < network_num; i++)
	{
		branches[i].current_t = nodes[i].node_voltage / resistance_t;
	}
}
//查询节点电压
double query_node_voltage(node_t* nodes, int network_num, int node_index)
{
	if (node_index >= 0 && node_index < network_num)
	{
		return nodes[node_index].node_voltage;
	}
	else
	{
		return -1;
	}
}
//查询支路电流RB
double query_branch_current_b(branch_t* branches, int network_num, int branch_index)
{
	if (branch_index >= 0 && branch_index < network_num)
	{
		return branches[branch_index].current_b;
	}
	else
	{
		return -1;
	}
}
//查询支路电流RT
double query_branch_current_t(branch_t* branches, int network_num, int branch_index)
{
	if (branch_index >= 0 && branch_index < network_num)
	{
		return branches[branch_index].current_t;
	}
	else
	{
		return -1;
	}
}