#include"main.h"
#include"config.h"
#include<math.h> //数学函数支持
//计算等效电阻
void calculate_req(int network_num, double resistance_b, double resistance_t, double resistance_l, double req[])
{
	req[network_num] = resistance_l;
	for (int i = network_num; i >= 1; i--)
	{
		//添加除零安全检查
		double denominator = resistance_t + req[i];
		if (denominator > DIVISION_EPSILON)
		{
			req[i-1] = (req[i] * resistance_t) / denominator;
			req[i-1] += resistance_b;
		}
		else
		{
			//异常情况处理：设置为默认值
			req[i-1] = resistance_b;
		}
	}
}
//计算节点电位
void calculate_node_voltage(node_t* nodes, int network_num, double input_voltage, double req[], double resistance_b, double resistance_t, double resistance_l)
{
	//计算总电流，添加除零安全检查
	double total_current = 0.0;
	if (req[0] > DIVISION_EPSILON)
	{
		total_current = input_voltage / req[0];
	}
	else
	{
		printf("警告：等效电阻过小，无法计算电流\n");
		return; //提前返回，避免后续计算错误
	}
	
	int i;
	for (i = 0; i < network_num; i++)
	{
		if (i == 0)
		{
			nodes[i].node_voltage = input_voltage - total_current * resistance_b;
		}
		else
		{
			//安全检查：防止数组越界访问
			if (i + 1 <= network_num)
			{
				double denominator = resistance_t + req[i + 1];
				if (denominator > DIVISION_EPSILON)
				{
					double temp = nodes[i - 1].node_voltage / denominator;
					nodes[i].node_voltage = nodes[i - 1].node_voltage - temp * resistance_b;
				}
				else
				{
					nodes[i].node_voltage = nodes[i - 1].node_voltage; //保持前一节点电压
				}
			}
			else
			{
				//边界情况：使用负载电阻作为替代
				double denominator = resistance_t + resistance_l;
				if (denominator > DIVISION_EPSILON)
				{
					double temp = nodes[i - 1].node_voltage / denominator;
					nodes[i].node_voltage = nodes[i - 1].node_voltage - temp * resistance_b;
				}
				else
				{
					nodes[i].node_voltage = nodes[i - 1].node_voltage; //保持前一节点电压
				}
			}
		}
	}
}
//计算支路电流
void calculate_branch_current(branch_t* branches, node_t* nodes, int network_num, double input_voltage, double resistance_b, double resistance_t)
{
	int i;
	//计算支路RB电流，添加除零检查
	if (resistance_b > DIVISION_EPSILON)
	{
		branches[0].current_b = (input_voltage - nodes[0].node_voltage) / resistance_b;
		for (i = 1; i < network_num; i++)
		{
			branches[i].current_b = (nodes[i - 1].node_voltage - nodes[i].node_voltage) / resistance_b;
		}
	}
	else
	{
		printf("警告：支路电阻RB过小，无法计算电流\n");
		for (i = 0; i < network_num; i++)
		{
			branches[i].current_b = 0.0; //设置为零电流
		}
	}
	//计算支路RT电流，添加除零检查
	if (resistance_t > DIVISION_EPSILON)
	{
		for (i = 0; i < network_num; i++)
		{
			branches[i].current_t = nodes[i].node_voltage / resistance_t;
		}
	}
	else
	{
		printf("警告：支路电阻RT过小，无法计算电流\n");
		for (i = 0; i < network_num; i++)
		{
			branches[i].current_t = 0.0; //设置为零电流
		}
	}
}
//查询节点电压
double query_node_voltage(node_t* nodes, int network_num, int node_index)
{
	if (node_index >= 0 && node_index < network_num)
	{
		return nodes[node_index].node_voltage;
	}
	else
	{
		return QUERY_ERROR_CODE;
	}
}
//查询支路电流RB
double query_branch_current_b(branch_t* branches, int network_num, int branch_index)
{
	if (branch_index >= 0 && branch_index < network_num)
	{
		return branches[branch_index].current_b;
	}
	else
	{
		return QUERY_ERROR_CODE;
	}
}
//查询支路电流RT
double query_branch_current_t(branch_t* branches, int network_num, int branch_index)
{
	if (branch_index >= 0 && branch_index < network_num)
	{
		return branches[branch_index].current_t;
	}
	else
	{
		return QUERY_ERROR_CODE;
	}
}

//内存管理安全函数实现
//安全释放电路计算所需的所有内存
void free_circuit_memory(double** req, node_t** nodes, branch_t** branches)
{
	//安全释放等效电阻数组
	if (req != NULL && *req != NULL)
	{
		free(*req);
		*req = NULL;
		printf("✅ 等效电阻数组内存已安全释放\n");
	}

	//安全释放节点电压数组
	if (nodes != NULL && *nodes != NULL)
	{
		free(*nodes);
		*nodes = NULL;
		printf("✅ 节点电压数组内存已安全释放\n");
	}

	//安全释放支路电流数组
	if (branches != NULL && *branches != NULL)
	{
		free(*branches);
		*branches = NULL;
		printf("✅ 支路电流数组内存已安全释放\n");
	}
}

//统一的内存分配函数，修正数组大小问题
int allocate_circuit_memory(int network_num, double** req, node_t** nodes, branch_t** branches)
{
	//参数有效性检查
	if (network_num < MIN_NETWORK_NUMBER || network_num > MAX_NETWORK_NUMBER)
	{
		printf("❌ 内存分配失败：网络序号 %d 超出有效范围 [%d-%d]\n",
			   network_num, MIN_NETWORK_NUMBER, MAX_NETWORK_NUMBER);
		return CALCULATION_FAILURE;
	}

	//分配等效电阻数组（大小为network_num+1以防越界）
	*req = (double*)malloc((network_num + 1) * sizeof(double));
	if (*req == NULL)
	{
		printf("💥 致命错误：等效电阻数组内存分配失败\n");
		printf("🔧 建议：减少网络序号或重启程序\n");
		return CALCULATION_FAILURE;
	}
	printf("✅ 等效电阻数组内存分配成功 [大小: %d]\n", network_num + 1);

	//分配节点电压数组
	*nodes = (node_t*)malloc(network_num * sizeof(node_t));
	if (*nodes == NULL)
	{
		printf("💥 致命错误：节点电压数组内存分配失败\n");
		free(*req);
		*req = NULL;
		return CALCULATION_FAILURE;
	}
	printf("✅ 节点电压数组内存分配成功 [大小: %d]\n", network_num);

	//分配支路电流数组
	*branches = (branch_t*)malloc(network_num * sizeof(branch_t));
	if (*branches == NULL)
	{
		printf("💥 致命错误：支路电流数组内存分配失败\n");
		free(*nodes);
		free(*req);
		*nodes = NULL;
		*req = NULL;
		return CALCULATION_FAILURE;
	}
	printf("✅ 支路电流数组内存分配成功 [大小: %d]\n", network_num);

	printf("🎉 所有内存分配完成，系统准备就绪\n");
	return CALCULATION_SUCCESS;
}

//输入验证函数实现
//网络序号有效性检查器
int validate_network_number(int network_num)
{
	//检查下界限制
	if (network_num < MIN_NETWORK_NUMBER)
	{
		printf("⚠️  输入错误：网络序号 %d 过小，最小允许值为 %d\n", network_num, MIN_NETWORK_NUMBER);
		printf("💡 建议：请输入大于等于 %d 的网络序号\n", MIN_NETWORK_NUMBER);
		return CALCULATION_FAILURE;
	}

	//检查上界限制
	if (network_num > MAX_NETWORK_NUMBER)
	{
		printf("⚠️  输入错误：网络序号 %d 过大，最大允许值为 %d\n", network_num, MAX_NETWORK_NUMBER);
		printf("💡 建议：请输入小于等于 %d 的网络序号\n", MAX_NETWORK_NUMBER);
		return CALCULATION_FAILURE;
	}

	printf("✅ 网络序号验证通过：%d 在有效范围内\n", network_num);
	return CALCULATION_SUCCESS;
}

//正数值有效性检查器
int validate_positive_value(double value, const char* value_name)
{
	//检查是否为正数
	if (value <= MIN_POSITIVE_VALUE)
	{
		printf("⚠️  输入错误：%s 值 %.6f 必须为正数\n", value_name, value);
		printf("💡 建议：请输入大于 %.2e 的正数值\n", MIN_POSITIVE_VALUE);
		return CALCULATION_FAILURE;
	}

	printf("✅ %s 验证通过：%.6f 为有效正数\n", value_name, value);
	return CALCULATION_SUCCESS;
}

//综合参数验证器
int validate_input_parameters(int network_num, double input_voltage, double resistance_b, double resistance_t, double resistance_l)
{
	printf("\n🔍 开始电路参数安全性检查...\n");
	printf("==================================================\n");

	int validation_errors = 0; //错误计数器

	//第一步：网络拓扑结构验证
	printf("📊 步骤1：网络拓扑结构验证\n");
	if (validate_network_number(network_num) != CALCULATION_SUCCESS)
	{
		validation_errors++;
	}

	//第二步：电源系统验证
	printf("\n⚡ 步骤2：电源系统验证\n");
	if (validate_positive_value(input_voltage, "输入电压") != CALCULATION_SUCCESS)
	{
		validation_errors++;
	}

	//第三步：电阻系统验证
	printf("\n🔧 步骤3：电阻系统验证\n");
	if (validate_positive_value(resistance_b, "支路电阻RB") != CALCULATION_SUCCESS)
	{
		validation_errors++;
	}
	if (validate_positive_value(resistance_t, "支路电阻RT") != CALCULATION_SUCCESS)
	{
		validation_errors++;
	}
	if (validate_positive_value(resistance_l, "负载电阻RL") != CALCULATION_SUCCESS)
	{
		validation_errors++;
	}

	//验证结果汇总
	printf("\n==================================================\n");
	if (validation_errors == 0)
	{
		printf("\n🎉 所有参数验证通过！电路参数符合物理规律和计算要求\n");
		printf("✨ 系统已准备好进行电路分析计算\n");
		return CALCULATION_SUCCESS;
	}
	else
	{
		printf("\n❌ 参数验证失败！发现 %d 个错误\n", validation_errors);
		printf("🔧 请根据上述建议修正输入参数后重试\n");
		return CALCULATION_FAILURE;
	}
}

//数值安全检查函数实现
//检查数值是否接近零
int is_zero_value(double value)
{
	//使用DIVISION_EPSILON作为判断标准
	if (fabs(value) <= DIVISION_EPSILON)
	{
		return 1; //接近零，返回真
	}
	else
	{
		return 0; //不接近零，返回假
	}
}

//安全除法运算检查器
int safe_division_check(double numerator, double denominator, double* result)
{
	//检查分母是否为零或接近零
	if (is_zero_value(denominator))
	{
		printf("⚠️  数值计算警告：检测到除零风险\n");
		printf("📊 分子值: %.6e, 分母值: %.6e\n", numerator, denominator);
		printf("🔧 建议：检查输入参数或电路配置\n");

		//设置安全的默认结果
		if (result != NULL)
		{
			*result = 0.0; //设置为零作为安全默认值
		}
		return CALCULATION_FAILURE;
	}

	//执行安全的除法运算
	if (result != NULL)
	{
		*result = numerator / denominator;

		//检查结果的数值稳定性
		if (isnan(*result) || isinf(*result))
		{
			printf("⚠️  数值计算警告：计算结果不稳定\n");
			printf("📊 计算结果: %.6e (可能为NaN或无穷大)\n", *result);
			*result = 0.0; //重置为安全值
			return CALCULATION_FAILURE;
		}

		printf("✅ 除法运算安全完成：%.6e / %.6e = %.6e\n", numerator, denominator, *result);
	}

	return CALCULATION_SUCCESS;
}
